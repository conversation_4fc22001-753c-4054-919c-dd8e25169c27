@page "/destinations/{DestinationId:guid}/gallery"
@using OdmoriBa.Application.Features.Destinations.Commands
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Files.Commands
@using OdmoriBa.Core.Domains.Files.Entities

@implements IDisposable

@inject DestinationState DestinationState
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

<PageTitle>Destinacija | Detalji</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Destinacije", href: "/destinations", icon: Icons.Material.Filled.ModeOfTravel),
                   new BreadcrumbItem(_destination?.Name!, href: $"/destinations/{_destination?.Id}", icon: Icons.Material.Filled.ModeOfTravel),
                   new BreadcrumbItem("Galerija", href: $"/destinations/{_destination?.Id}/gallery", icon: Icons.Material.Filled.Photo),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudStack Style="width: 100%">
    <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                   @ref="@_fileUpload"
                   OnFilesChanged="@(_ => ClearDragClass())"
                   @bind-Files="Files"
                   AppendMultipleFiles
                   Hidden="@false"
                   Accept=".png, .jpg, .jpeg"
                   InputClass="absolute mud-width-full mud-height-full overflow-hidden z-10"
                   InputStyle="opacity:0"
                   tabindex="-1"
                   @ondrop="@ClearDragClass"
                   @ondragenter="@SetDragClass"
                   @ondragleave="@ClearDragClass"
                   @ondragend="@ClearDragClass">
        <ActivatorContent>
            <MudPaper Height="200px"
                      Outlined="true"
                      Class="@_dragClass">
                <MudText Typo="Typo.h6">
                    Prevuci slike ili klikni za upload
                </MudText>
                @foreach (var file in Files)
                {
                    <MudChip T="string"
                             Color="Color.Dark"
                             Text="@file.Name"
                             tabindex="-1"/>
                }
            </MudPaper>
        </ActivatorContent>
    </MudFileUpload>
    <MudToolBar Gutters="false"
                Class="relative d-flex justify-end gap-4">
        <MudButton Color="Color.Primary"
                   OnClick="@OpenFilePickerAsync"
                   Variant="Variant.Filled">
            Odaberi slike
        </MudButton>
        <MudButton Color="Color.Primary"
                   Disabled="@(!Files.Any())"
                   OnClick="@UploadAsync"
                   Variant="Variant.Filled">
            Upload
        </MudButton>
        <MudButton Color="Color.Error"
                   Disabled="@(!Files.Any())"
                   OnClick="@ClearAsync"
                   Variant="Variant.Filled">
            Očisti
        </MudButton>
    </MudToolBar>
</MudStack>
<br/>

@if (_destination?.Images?.Any() == true)
{
    <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
        <MudTabPanel Text="Pregled slika" Icon="@Icons.Material.Filled.GridView">
            <MudGrid>
                @foreach (var image in _destination.Images)
                {
                    <MudItem xs="12" sm="6" md="4" lg="3">
                        <MudCard Class="ma-2" Style="height: 100%;">
                            <MudCardMedia Image="@image.Url" Height="200" />
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">
                                    @(string.IsNullOrEmpty(image.Title) ? "Bez naslova" : image.Title)
                                </MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3" Style="min-height: 40px;">
                                    @(string.IsNullOrEmpty(image.Description) ? "Bez opisa" : image.Description)
                                </MudText>
                            </MudCardContent>
                            <MudCardActions Class="d-flex justify-space-between">
                                <MudButton StartIcon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          OnClick="@(() => UpdateImageAsync(image))">
                                    Uredi
                                </MudButton>
                                <MudButton StartIcon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteImageAsync(image))">
                                    Obriši
                                </MudButton>
                            </MudCardActions>
                        </MudCard>
                    </MudItem>
                }
            </MudGrid>
        </MudTabPanel>

        <MudTabPanel Text="Galerija" Icon="@Icons.Material.Filled.PhotoLibrary">
            <MudGallery @ref="_gallery" ImageSource="_imageUrls" ItemPerLine="10" EnableAnimation="true">
                <ToolboxTopContent>
                    <MudText Class="white-text pa-4">@_destination?.Images?[_gallery.GetSelectedImageIndex()].Title</MudText>
                </ToolboxTopContent>

                <ToolboxBottomContent>
                    <MudText Class="white-text pa-4">@_destination?.Images?[_gallery.GetSelectedImageIndex()].Description</MudText>
                    <MudIconButton Class="white-text" OnClick="@(() => UpdateImageAsync(_destination?.Images?[_gallery.GetSelectedImageIndex()]))" Icon="@Icons.Material.Outlined.Edit"/>
                </ToolboxBottomContent>
            </MudGallery>
        </MudTabPanel>
    </MudTabs>
}
else
{
    <MudAlert Severity="Severity.Info" Class="mt-4">
        Nema uploadovanih slika. Koristite upload funkciju iznad da dodate slike u galeriju.
    </MudAlert>
}

@code {
    [Parameter] public Guid DestinationId { get; set; }

    private DestinationDto? _destination;

    private bool _loading;

    private MudGallery _gallery = null!;

    private List<string> _imageUrls = [];

    private MudFileUpload<IReadOnlyList<IBrowserFile>>? _fileUpload;
    private IReadOnlyList<IBrowserFile> Files { get; set; } = new List<IBrowserFile>();
    private const string DefaultDragClass = "relative rounded-lg border-2 border-dashed pa-4 mt-4 mud-width-full mud-height-full";
    private string _dragClass = DefaultDragClass;
    
    protected override async Task OnParametersSetAsync()
    {
        _destination = await DestinationState.LoadDestinationAsync(DestinationId);
        _imageUrls = _destination.Images?.Select(s => s.Url!).ToList() ?? [];
    }
    
    protected override void OnInitialized()
    {
        DestinationState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        DestinationState.OnChange -= StateHasChanged;
    }

    private Task ClearAsync()
    {
        Files = [];
        ClearDragClass();
        return Task.CompletedTask;
    }

    private Task OpenFilePickerAsync()
        => _fileUpload?.OpenFilePickerAsync() ?? Task.CompletedTask;

    private async Task UploadAsync()
    {
        _loading = true;
        foreach (var file in Files)
        {
            var result = await Mediator.Send(new CreateFileRecordCommand(
                FileRecordType.DestinationImage,
                file.Name,
                $"destinations/{DestinationId}/gallery/{file.Name}",
                file.OpenReadStream(),
                file.ContentType,
                file.Size
            ));

            await result.SwitchAsync(async (val) =>
            {
                var destImageResult = await Mediator.Send(new CreateDestinationImageCommand(
                    DestinationId,
                    val.Id,
                    null, null
                ));

                destImageResult.Switch(destinationImage =>
                {
                    // Add to both DestinationState and local _destination
                    DestinationState.Destination?.Images?.Add(destinationImage);
                    _destination?.Images?.Add(destinationImage);
                    _imageUrls.Add(destinationImage.Url!);
                }, error => Snackbar.Add($"Create destination image {file.Name} failed: {error.Description}", Severity.Error));
            }, error =>
            {
                Snackbar.Add($"Upload image {file.Name} failed: {error.Description}", Severity.Error);
                return Task.CompletedTask;
            });
        }

        _loading = false;
        Files = [];
    }

    private void SetDragClass()
        => _dragClass = $"{DefaultDragClass} mud-border-primary";

    private void ClearDragClass()
        => _dragClass = DefaultDragClass;


    private async Task UpdateImageAsync(DestinationImageDto? data = null)
    {
        if (data == null) return;

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Small,
            FullWidth = true,
            CloseOnEscapeKey = true,
            BackdropClick = false
        };
        var parameters = new DialogParameters<UpdateGalleryImageDialog>
        {
            { x => x.Data, data },
            { x => x.DestinationId, DestinationId }
        };

        var dialog = await DialogService.ShowAsync<UpdateGalleryImageDialog>($"Uredi sliku", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var updatedImage = (result.Data as DestinationImageDto)!;

            // Update both DestinationState and local _destination
            var stateIndex = DestinationState.Destination!.Images!.FindIndex(s => s.Id == data.Id);
            if (stateIndex >= 0)
            {
                DestinationState.Destination.Images[stateIndex] = updatedImage;
            }

            var localIndex = _destination!.Images!.FindIndex(s => s.Id == data.Id);
            if (localIndex >= 0)
            {
                _destination.Images[localIndex] = updatedImage;
            }

            StateHasChanged();
        }
    }



    private async Task DeleteImageAsync(DestinationImageDto image)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "Potvrdi brisanje",
            $"Da li ste sigurni da želite obrisati sliku '{image.Title ?? "Bez naslova"}'?",
            yesText: "Da", cancelText: "Ne");

        if (confirmed == true)
        {
            _loading = true;
            var result = await Mediator.Send(new RemoveDestinationImageCommand(DestinationId, image.Id));

            result.Switch(
                () =>
                {
                    // Remove from both DestinationState and local _destination
                    DestinationState.Destination?.Images?.Remove(image);
                    _destination?.Images?.Remove(image);
                    _imageUrls.Remove(image.Url!);
                    StateHasChanged();
                    Snackbar.Add("Slika je uspješno obrisana", Severity.Success);
                },
                error => Snackbar.Add($"Greška pri brisanju slike: {error.Description}", Severity.Error)
            );
            _loading = false;
        }
    }
}