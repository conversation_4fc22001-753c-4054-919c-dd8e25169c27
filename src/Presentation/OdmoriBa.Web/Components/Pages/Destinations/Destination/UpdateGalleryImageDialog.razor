@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Destinations.Mappers
@using OdmoriBa.Presentation.Features.Destinations.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog Style="z-index: 99999 !important;" Class="dialog-overlay">
    <TitleContent>
        <MudStack Row="true" AlignItems="Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Edit" />
            <MudText Typo="Typo.h6">@MudDialog!.Title</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }" Class="mb-4">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="4">
                <MudTextField Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              @bind-Value="_request.Title"
                              For="@(() => _request.Title)"
                              Immediate="true"
                              Label="Naslov"
                              HelperText="Unesite naslov slike (maksimalno 100 karaktera)"
                              Counter="100"/>

                <MudTextField Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              @bind-Value="_request.Description"
                              For="@(() => _request.Description)"
                              Lines="4"
                              Immediate="true"
                              Label="Opis"
                              HelperText="Unesite opis slike (maksimalno 500 karaktera)"
                              Counter="500"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel"
                   Disabled="_loading"
                   OnClick="Cancel"
                   Variant="Variant.Text">
            Otkazi
        </MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save"
                   Disabled="_loading"
                   Color="Color.Primary"
                   Variant="Variant.Filled"
                   OnClick="Submit">
            @if (_loading)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">Spremam...</MudText>
            }
            else
            {
                <MudText>Spremi</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .dialog-overlay {
        position: fixed !important;
        z-index: 99999 !important;
    }

    .mud-dialog-container {
        z-index: 99999 !important;
    }
</style>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public Guid DestinationId { get; set; }
    [Parameter] [EditorRequired] public DestinationImageDto Data { get; set; } = null!;

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private readonly UpdateDestinationImageRequest _request = new();
    private readonly UpdateDestinationImageRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _request.Title = Data.Title;
        _request.Description = Data.Description;
    }

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(DestinationId, Data.Id));

        result.Switch(value =>
        {
            Snackbar.Add("Image is updated", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();
}